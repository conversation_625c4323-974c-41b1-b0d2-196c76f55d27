import { AppLanguage, LANGUAGE } from "../types/typing";

/**
 * Trigger phrases that customers can send to disable the chatbot and request human assistance
 */
export const CHATBOT_DISABLE_TRIGGERS = {
    ENGLISH: [
        "quick reply",
        "admin reply",
        "human help",
        "speak to admin",
        "talk to human",
        "customer service",
        "live chat"
    ],
    PERSIAN: [
        "ادمین پاسخ دهد",
        "پاسخ سریع",
        "صحبت با ادمین",
        "کمک انسانی",
        "پشتیبانی",
        "خدمات مشتریان"
    ]
};

/**
 * Confirmation messages sent when chatbot is disabled due to trigger phrases
 */
export const CHATBOT_DISABLE_CONFIRMATIONS = {
    ENGLISH: "Your request for admin assistance has been received. You will get a response soon.",
    PERSIAN: "درخواست شما برای صحبت با ادمین ثبت شد. به زودی پاسخ خواهید گرفت."
};

/**
 * System notification messages saved to chat history when admin assistance is requested
 */
export const ADMIN_ASSISTANCE_NOTIFICATIONS = {
    ENGLISH: "🔔 Customer requested admin assistance - Chatbot disabled",
    PERSIAN: "🔔 مشتری درخواست کمک ادمین کرده - ربات چت غیرفعال شد"
};

/**
 * Get all trigger phrases for the current language
 */
export function getTriggerPhrases(language: AppLanguage): string[] {
    if (language === LANGUAGE.ENGLISH) {
        return [...CHATBOT_DISABLE_TRIGGERS.ENGLISH, ...CHATBOT_DISABLE_TRIGGERS.PERSIAN];
    }
    return [...CHATBOT_DISABLE_TRIGGERS.PERSIAN, ...CHATBOT_DISABLE_TRIGGERS.ENGLISH];
}

/**
 * Get confirmation message for the current language
 */
export function getConfirmationMessage(language: AppLanguage): string {
    return language === LANGUAGE.ENGLISH
        ? CHATBOT_DISABLE_CONFIRMATIONS.ENGLISH
        : CHATBOT_DISABLE_CONFIRMATIONS.PERSIAN;
}

/**
 * Get admin assistance notification message for the current language
 */
export function getAdminAssistanceNotification(language: AppLanguage): string {
    return language === LANGUAGE.ENGLISH
        ? ADMIN_ASSISTANCE_NOTIFICATIONS.ENGLISH
        : ADMIN_ASSISTANCE_NOTIFICATIONS.PERSIAN;
}
